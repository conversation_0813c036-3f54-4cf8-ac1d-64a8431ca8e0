[{"question": "<PERSON><PERSON> dieser Spiele hat Rainer in einem legendären Stream ragequit gemacht?", "choices": ["Minecraft", "Dark Souls", "Landwirtschafts-Simulator", "Fortnite"], "answer": 1}, {"question": "Was ist Rainers ikonischstes Lebensmittel?", "choices": ["<PERSON><PERSON><PERSON>", "Mett", "Pizza", "Leberkäse"], "answer": 1}, {"question": "Wie viele Wohnungen hatte Rainer seit dem Abriss der Schanze?", "choices": ["2", "4", "6", "8"], "answer": 2}, {"question": "Wie lautet Rainers zweiter Vorname?", "choices": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "answer": 2}, {"question": "In welchem Jahr startete <PERSON><PERSON> seinen YouTube-Kanal?", "choices": ["2011", "2012", "2013", "2014"], "answer": 0}, {"question": "Welches Instrument behauptet Rainer spielen zu können?", "choices": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Schlagzeug", "Bass"], "answer": 1}, {"question": "Wie nennt Rainer seine Fans?", "choices": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Fanbase", "<PERSON><PERSON><PERSON>"], "answer": 0}, {"question": "Was ist <PERSON><PERSON> Lieblingsfarbe?", "choices": ["Blau", "Rot", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "answer": 0}, {"question": "Welches Tier hatte Rainer nie als Haustier?", "choices": ["<PERSON><PERSON>", "<PERSON>nd", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "answer": 3}, {"question": "In welchem Bundesland liegt die 'Schanze'?", "choices": ["Bayern", "Sachsen", "T<PERSON><PERSON><PERSON>en", "Hessen"], "answer": 0}, {"question": "Was ist <PERSON><PERSON> Lieblings-Videospielreihe?", "choices": ["Call of Duty", "<PERSON><PERSON><PERSON>", "Pokemon", "<PERSON>"], "answer": 0}, {"question": "Welchen Job hat Rainer nie ausgeübt?", "choices": ["Maler", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Maurer"], "answer": 2}, {"question": "Was war Rainers erstes Auto?", "choices": ["Renault", "Opel", "Ford", "VW"], "answer": 0}, {"question": "Wie lautet sein bekannter Ausruf?", "choices": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "answer": 0}, {"question": "Was war Rainers erste <PERSON>?", "choices": ["Super Nintendo", "Playstation 1", "Nintendo 64", "Sega Mega Drive"], "answer": 1}, {"question": "Welcher dieser Orte liegt NICHT in Altschauerberg?", "choices": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pizzeria Rizzo", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "answer": 3}, {"question": "Was ist Rainers Lieblingsgericht bei 'seiner' Pizzeria?", "choices": ["Pizza Hawaii", "Pizza Speciale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Calzone"], "answer": 1}, {"question": "<PERSON><PERSON> dieser Tiere hatte Rainer als letztes?", "choices": ["<PERSON><PERSON>", "<PERSON>nd <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "answer": 0}, {"question": "In welchem Jahr wurde die 'Schanze' abgerissen?", "choices": ["2021", "2022", "2023", "2020"], "answer": 1}, {"question": "<PERSON><PERSON> dieser Games hat Rain<PERSON> NIE gespielt?", "choices": ["Kingdom Come: Deliverance", "Elder Scrolls Online", "World of Warcraft", "Final Fantasy XIV"], "answer": 3}, {"question": "Wie lautet der Name von <PERSON> YouTube-Kanal?", "choices": ["Dr<PERSON><PERSON><PERSON>", "DracheGaming", "DrachenLP", "DrachenGamesLP"], "answer": 2}, {"question": "Was war Rainers erste 'richtige' Arbeit?", "choices": ["<PERSON><PERSON><PERSON>", "Maler", "<PERSON>", "Maurer"], "answer": 1}, {"question": "<PERSON><PERSON> dieser Spiele streamte Rainer am häufigsten?", "choices": ["GTA V", "Call of Duty", "Minecraft", "World of Tanks"], "answer": 1}, {"question": "Was ist Rainers typische Begrüßung in seinen Videos?", "choices": ["<PERSON><PERSON><PERSON> und <PERSON>o", "Meddl Loide", "Guten Tag zusammen", "<PERSON><PERSON><PERSON>"], "answer": 1}, {"question": "Welches Fahrzeug fuhr Rainer NIE?", "choices": ["Suzuki Swift", "Renault Clio", "Ford Focus", "VW Golf"], "answer": 3}, {"question": "<PERSON><PERSON> dieser Instrumente besaß Rainer wirklich?", "choices": ["E-Gitarre", "Schlagzeug", "Keyboard", "Bass"], "answer": 0}, {"question": "Was war Rainers ursprünglicher Berufswunsch?", "choices": ["<PERSON>", "Polizist", "Pilot", "Youtuber"], "answer": 1}, {"question": "In welchem Monat hat Rainer Geburtstag?", "choices": ["<PERSON><PERSON>", "<PERSON><PERSON>", "August", "September"], "answer": 2}, {"question": "<PERSON><PERSON> dieser Spiele hat Rainer tatsächlich durchgespielt?", "choices": ["Dark Souls", "Skyrim", "GTA V", "Pokemon"], "answer": 2}, {"question": "Welcher Haider ist bekannt für den 'Schanzenfund'?", "choices": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SterniFee"], "answer": 0}, {"question": "An welchem Tag wurde die Schanze zwangsgeräumt?", "choices": ["20. Ok<PERSON>ber 2021", "21. <PERSON><PERSON><PERSON> 2021", "22. <PERSON><PERSON><PERSON> 2021", "23. <PERSON><PERSON><PERSON> 2021"], "answer": 1}, {"question": "Was war der Name von Rainers erster Band?", "choices": ["Dr<PERSON><PERSON><PERSON>", "Drache of Pain", "Phoenix from the Ashes", "Dragon Fire"], "answer": 2}, {"question": "Wie viele Haustiere hatte Rainer insgesamt auf der Schanze?", "choices": ["5", "7", "9", "11"], "answer": 2}, {"question": "<PERSON><PERSON> dieser YouTuber hat KEINEN Besuch bei Rainer gemacht?", "choices": ["Tanzverbot", "MontanaBlack", "KuchenTV", "ApoRed"], "answer": 1}, {"question": "Was war der Name von Rainers erstem Discord-Server?", "choices": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Drachenclub", "<PERSON><PERSON><PERSON><PERSON>", "Dr<PERSON><PERSON><PERSON>"], "answer": 2}, {"question": "Welches war sein erstes Smartphone?", "choices": ["iPhone 4", "Samsung Galaxy S3", "HTC One", "Sony Xperia"], "answer": 1}, {"question": "Was kostete urs<PERSON><PERSON><PERSON><PERSON><PERSON> ein 'Meet & Greet' mit <PERSON>?", "choices": ["25€", "50€", "100€", "150€"], "answer": 2}, {"question": "<PERSON>er dieser Songs ist NICHT von <PERSON>?", "choices": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Arbeitslos", "Drachenlied"], "answer": 2}, {"question": "Wie lautete Rainers erste Handynummer, die er leakte?", "choices": ["0151...", "0160...", "0170...", "0176..."], "answer": 0}, {"question": "Was war der ursprüngliche Name seines YouTube-Kanals?", "choices": ["Dragon Gaming", "DracheOfGames", "DrachenGamesLP", "DrachenLordRW"], "answer": 3}, {"question": "<PERSON><PERSON> hat <PERSON> in seinem Koch-Stream verbrannt?", "choices": ["Pizzaburger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Spaghetti"], "answer": 0}, {"question": "In welchem Jahr startete das 'Game' offiziell?", "choices": ["2013", "2014", "2015", "2016"], "answer": 1}, {"question": "Was war die höchste Besucherzahl bei einer 'Schanzenfest'-Demo?", "choices": ["500", "800", "1000", "1500"], "answer": 2}, {"question": "Welcher Journalist hat die erste große Doku über Rainer gemacht?", "choices": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "answer": 0}, {"question": "<PERSON><PERSON> dieser Spiele hat Rainer nie gespielt?", "choices": ["Minecraft", "Skyrim", "The Witcher 3", "GTA IV"], "answer": 2}, {"question": "Was ist Rainers meistgesperrtes Social-Media-Konto?", "choices": ["YouTube", "Twitter", "Twitch", "TikTok"], "answer": 0}, {"question": "Welche Aussage hat Rainer im Stream g<PERSON>agt?", "choices": ["<PERSON><PERSON> bin der intelligenteste Mensch", "<PERSON><PERSON> bin härter als Leben", "Ich habe Abi gemacht", "<PERSON>ch bin Informatiker"], "answer": 1}, {"question": "<PERSON><PERSON> dieser Getränke bevorzugt <PERSON>?", "choices": ["Fanta", "Cola", "Spezi", "<PERSON><PERSON><PERSON>"], "answer": 2}, {"question": "Welche Farbe hatte Rainers berühmtes Stirnband?", "choices": ["Rot", "Blau", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "answer": 0}, {"question": "<PERSON><PERSON> dieser Songs hat Rainer gec<PERSON>t?", "choices": ["Schrei nach Liebe", "Smoke on the Water", "Thunderstruck", "<PERSON><PERSON>"], "answer": 0}, {"question": "Mit welchem Fahrzeug wurde Rainer 2023 gesehen?", "choices": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>er", "Roller", "Auto"], "answer": 1}, {"question": "Welches Format hat Rainer am längsten durchgezogen?", "choices": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Let’s Plays", "Vlogs", "Reactions"], "answer": 2}, {"question": "<PERSON><PERSON> E<PERSON> hat Rainer selbst gemacht und als 'Gourmet' bezeichnet?", "choices": ["Nudeln mit Ketchup", "Rührei mit Käse", "Pizza mit Chips", "Toast mit Nutella und Salami"], "answer": 3}, {"question": "<PERSON><PERSON> dieser <PERSON>nder hat Rainer besucht?", "choices": ["Spanien", "Italien", "<PERSON><PERSON><PERSON>", "Österreich"], "answer": 3}, {"question": "Wie nennt Rainer seine 'Projekte' oft?", "choices": ["Zukunftsvisionen", "Mega-Events", "Langzeitprojekte", "Drachenprojekte"], "answer": 2}, {"question": "Welch<PERSON> Behauptung hat Rainer über seine Kampfkünste gemacht?", "choices": ["Ich kann mit einem Schlag töten", "Ich bin ein ausgebildeter Kämpfer", "Ich habe Ninja-Training gemacht", "Ich kann mit Schwert kämpfen"], "answer": 0}, {"question": "Welcher Film gehört zu Rainers Lieblingsfilmen?", "choices": ["The Fast and the Furious", "<PERSON>", "<PERSON>", "Matrix"], "answer": 1}, {"question": "Welche Art von Musik macht Rainer?", "choices": ["Rap", "Metal", "<PERSON><PERSON><PERSON>", "Pop"], "answer": 1}, {"question": "Wie viele Abonnenten hatte Rainers Hauptkanal vor der endgültigen Löschung?", "choices": ["50.000", "100.000", "150.000", "200.000"], "answer": 1}, {"question": "Was hat Rainer einmal als seine geheime Superkraft bezeichnet?", "choices": ["Unbesiegbarkeit", "Gedankenkontrolle", "Megaintelligenz", "Drachenatem"], "answer": 0}, {"question": "Wie heißt eine von Rain<PERSON> 'Freundinnen', die er öffentlich erwähnt hat?", "choices": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "answer": 1}, {"question": "<PERSON><PERSON> dieser Berufe hat Rainer fälschlicherweise für sich beansprucht?", "choices": ["Elektriker", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Informatiker"], "answer": 3}, {"question": "Welche Stadt hat Rainer als seinen Wohnort nach der Schanze angegeben?", "choices": ["Berlin", "Hamburg", "Nürnberg", "Leipzig"], "answer": 2}, {"question": "Welches Wort benutzt Rainer oft als Beleidigung?", "choices": ["Honk", "Depp", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "answer": 2}, {"question": "<PERSON><PERSON>art hat Rainer als sein Hobby bezeichnet?", "choices": ["Boxen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bodybuilding", "<PERSON><PERSON><PERSON>"], "answer": 0}, {"question": "<PERSON>es YouTube-Format hat Rainer 2022 ausprobiert?", "choices": ["Minecraft Let's Play", "Kochvideos", "Fitness-Vlogs", "Talk<PERSON>den"], "answer": 1}, {"question": "Welche Plattform hat Rainer zuletzt für Streams genutzt?", "choices": ["Twitch", "Kick", "YouTube", "TikTok"], "answer": 3}, {"question": "Welche Aussage hat Rainer zu seiner finanziellen Lage gemacht?", "choices": ["<PERSON><PERSON> bin <PERSON>", "Ich habe ausgesorgt", "Ich habe mehr Geld als ihr", "<PERSON>ch lebe von <PERSON>"], "answer": 2}, {"question": "Wie viele Bands hat Rainer angeblich gehabt?", "choices": ["1", "2", "3", "4"], "answer": 1}, {"question": "Was hat Rainer als seinen Traumberuf bezeichnet?", "choices": ["YouTuber", "<PERSON><PERSON><PERSON>", "Autor", "<PERSON><PERSON><PERSON><PERSON>"], "answer": 0}, {"question": "<PERSON><PERSON> dieser Aussagen hat Rainer über Frauen gemacht?", "choices": ["Ich hatte 100 Frauen", "Frauen stehen auf mich", "<PERSON>ch bin ein <PERSON>", "Frauen verstehen mich nicht"], "answer": 1}, {"question": "<PERSON><PERSON> dieser Plattformen nutzt Rainer für bezahlte Inhalte?", "choices": ["OnlyFans", "Patreon", "Ko-Fi", "Tipeee"], "answer": 1}, {"question": "Welche Aussage hat Rainer über seine körperliche Fitness gemacht?", "choices": ["Ich bin athletisch", "Ich bin fitter als ihr", "Ich trainiere jeden Tag", "Ich brauche keine Fitness"], "answer": 1}]
services:
  buttergolem_live:
    build: .
    volumes:
      - ./data:/app/data # Persistent storage for game data

    environment:
    
      DISCORD_API_TOKEN: "MTMyOTEwNDE5OTc5NDk1NDI0MA.Gwu8m4.xZfWBXrI6A_d99MJCp8x_UPrmMLL-a2mVsa-5w" # Discord Bot-Token
      ENABLE_RANDOM_JOINS: "False" # Enables random joins on the biggest VC
      BLACKLISTED_GUILDS: "123456,654321" # Comma seperated

      ADMIN_USER_ID: "219181139833651200" # CAREFUL also able to use Admin commands

      # Mongodb

      MONGODB_CONNECTION_STRING: "mongodb+srv://nindscher:<EMAIL>/?retryWrites=true&w=majority&appName=buttergolem1"
      MONGODB_DATABASE_NAME: "buttergolem1"
      MONGODB_TIMEOUT: "5000"
      MONGODB_POOL_SIZE: "50"
      ENABLE_MONGODB: "false"  # Feature flag - <PERSON><PERSON> de<PERSON>t wird alles in Json gerendert

      # Router Keys

      OPENROUTER_KEY: "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"
      VOID_API_KEY: "sk-voidai-MbIrlVbR5DnOzN0ZFDWyPK79l53bP6CxTHkZxZJhnKLNkDc8tYoSr9ni0lFns5pynwr5UgGZqxtuTHpp7LCGsMp69p2apXDKFNZC-free"
      
      # Channel spoecific

      LOGGING_CHANNEL: "1329478135443488769" # Logging Channel ID
      CHAT_MIRROR_CHANNEL: "1380484078717112330"
      MEMBER_COUNTER_SERVER: "1298341316291203102"

      # etc. 

      DISCORDS_KEY: "f3b5f712a44c971d5cd11e1ff262116c930e1df3710d1745f2021ad692a16ed4"
      TOPGG_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJib3QiOiJ0cnVlIiwiaWQiOiIxMzI5MTA0MTk5Nzk0OTU0MjQwIiwiaWF0IjoiMTc0MzI0MzE0OCJ9.nrvnV5QmqGnF2OF74WOOeCpF_oydgNgOgZwq7d9ew4o"

      # Monero Wallet für Spenden
      MONERO_SPENDEN_ID: "488jjkw5ZmcCgQdUKJ9AYUCqWhJtARpeHXFjHvjTeMt8VzqyKeFdLTYWhbcgUUfgxo2XJy43oRWwGCywJac8s2Jp6fRgYpH"
